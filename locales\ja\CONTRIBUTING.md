# Clineへの貢献

Clineへの貢献に興味をお持ちいただきありがとうございます。

## バグや問題の報告

バグ報告は、Clineを皆さんにとってより良いものにするために役立ちます！新しい問題を作成する前に、重複を避けるために[既存の問題を検索](https://github.com/cline/cline/issues)してください。バグを報告する準備ができたら、[問題ページ](https://github.com/cline/cline/issues/new/choose)に移動し、関連情報を記入するためのテンプレートをご利用ください。

<blockquote class='warning-note'>
    🔐 <b>重要:</b> セキュリティ脆弱性を発見した場合は、<a href="https://github.com/cline/cline/security/advisories/new">Githubセキュリティツールを使用して非公開で報告</a>してください。
</blockquote>

## 作業内容の決定

最初の貢献をお探しですか？["good first issue"](https://github.com/cline/cline/labels/good%20first%20issue)や["help wanted"](https://github.com/cline/cline/labels/help%20wanted)のラベルが付いた問題をチェックしてください。これらは新しい貢献者向けに特に選ばれたもので、私たちが助けを求めている分野です！

また、[ドキュメント](https://github.com/cline/cline/tree/main/docs)への貢献も歓迎します！誤字の修正、既存のガイドの改善、新しい教育コンテンツの作成など、コミュニティ主導のリソースリポジトリを構築するために皆さんの力をお借りしたいと考えています。`/docs`に飛び込んで、改善が必要な箇所を探してみてください。

大きな機能に取り組む予定がある場合は、まず[機能リクエスト](https://github.com/cline/cline/discussions/categories/feature-requests?discussions_q=is%3Aopen+category%3A%22Feature+Requests%22+sort%3Atop)を作成し、それがClineのビジョンに合致するかどうかを議論しましょう。

## 開発環境のセットアップ

1. **VS Code拡張機能**

    - プロジェクトを開くと、VS Codeは推奨される拡張機能のインストールを促します
    - これらの拡張機能は開発に必要です - すべてのインストールプロンプトを受け入れてください
    - プロンプトを閉じた場合は、拡張機能パネルから手動でインストールできます

2. **ローカル開発**
    - `npm run install:all`を実行して依存関係をインストールします
    - `npm run test`を実行してローカルでテストを実行します
    - PRを提出する前に、`npm run format:fix`を実行してコードをフォーマットします

## コードの作成と提出

誰でもClineにコードを貢献できますが、貢献がスムーズに統合されるように以下のガイドラインに従ってください：

1. **プルリクエストを集中させる**

    - PRは単一の機能またはバグ修正に限定してください
    - 大きな変更は小さな関連PRに分割してください
    - 論理的なコミットに分けて、独立してレビューできるようにしてください

2. **コード品質**

    - `npm run lint`を実行してコードスタイルをチェックします
    - `npm run format`を実行してコードを自動的にフォーマットします
    - すべてのPRは、リンティングとフォーマットを含むCIチェックに合格する必要があります
    - 提出前にESLintの警告やエラーをすべて解決してください
    - TypeScriptのベストプラクティスに従い、型の安全性を維持してください

3. **テスト**

    - 新しい機能にはテストを追加してください
    - `npm test`を実行してすべてのテストが合格することを確認してください
    - 変更が既存のテストに影響を与える場合は、それらを更新してください
    - 適切な場合には、ユニットテストと統合テストの両方を含めてください

4. **コミットガイドライン**

    - 明確で説明的なコミットメッセージを書いてください
    - 従来のコミット形式（例："feat:", "fix:", "docs:"）を使用してください
    - コミットで関連する問題を#issue-numberを使用して参照してください

5. **提出前に**

    - 最新のmainにブランチをリベースしてください
    - ブランチが正常にビルドされることを確認してください
    - すべてのテストが合格していることを再確認してください
    - デバッグコードやコンソールログがないか変更を確認してください

6. **プルリクエストの説明**
    - 変更内容を明確に説明してください
    - 変更をテストする手順を含めてください
    - 破壊的な変更がある場合はリストしてください
    - UIの変更にはスクリーンショットを追加してください

## 貢献契約

プルリクエストを提出することで、あなたの貢献がプロジェクトと同じライセンス（[Apache 2.0](LICENSE)）の下でライセンスされることに同意したことになります。

覚えておいてください：Clineへの貢献はコードを書くことだけではなく、AI支援開発の未来を形作るコミュニティの一員になることです。一緒に素晴らしいものを作りましょう！🚀
