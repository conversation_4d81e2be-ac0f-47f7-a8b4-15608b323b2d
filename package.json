{"name": "claude-dev", "displayName": "Cline", "description": "Autonomous coding agent right in your IDE, capable of creating/editing files, running commands, using the browser, and more with your permission every step of the way.", "version": "3.3.2", "icon": "assets/icons/icon.png", "galleryBanner": {"color": "#617A91", "theme": "dark"}, "engines": {"vscode": "^1.84.0"}, "author": {"name": "Cline Bot Inc."}, "license": "Apache-2.0", "publisher": "<PERSON><PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/cline/cline"}, "homepage": "https://cline.bot", "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Education", "Snippets", "Testing"], "keywords": ["cline", "claude", "dev", "mcp", "openrouter", "coding", "agent", "autonomous", "chatgpt", "sonnet", "ai", "llama"], "activationEvents": [], "main": "./dist/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "claude-dev-ActivityBar", "title": "Cline", "icon": "assets/icons/icon.svg"}]}, "views": {"claude-dev-ActivityBar": [{"type": "webview", "id": "claude-dev.<PERSON>", "name": ""}]}, "commands": [{"command": "cline.plusButtonClicked", "title": "New Task", "icon": "$(add)"}, {"command": "cline.mcpButtonClicked", "title": "MCP Servers", "icon": "$(server)"}, {"command": "cline.historyButtonClicked", "title": "History", "icon": "$(history)"}, {"command": "cline.popoutButtonClicked", "title": "Open in Editor", "icon": "$(link-external)"}, {"command": "cline.settingsButtonClicked", "title": "Settings", "icon": "$(settings-gear)"}, {"command": "cline.openInNewTab", "title": "Open In New Tab", "category": "Cline"}], "menus": {"view/title": [{"command": "cline.plusButtonClicked", "group": "navigation@1", "when": "view == claude-dev.<PERSON>"}, {"command": "cline.mcpButtonClicked", "group": "navigation@2", "when": "view == claude-dev.<PERSON>"}, {"command": "cline.historyButtonClicked", "group": "navigation@3", "when": "view == claude-dev.<PERSON>"}, {"command": "cline.popoutButtonClicked", "group": "navigation@4", "when": "view == claude-dev.<PERSON>"}, {"command": "cline.settingsButtonClicked", "group": "navigation@5", "when": "view == claude-dev.<PERSON>"}]}, "configuration": {"title": "Cline", "properties": {"cline.vsCodeLmModelSelector": {"type": "object", "properties": {"vendor": {"type": "string", "description": "The vendor of the language model (e.g. copilot)"}, "family": {"type": "string", "description": "The family of the language model (e.g. gpt-4)"}}, "description": "Settings for VSCode Language Model API"}, "cline.mcp.mode": {"type": "string", "enum": ["full", "server-use-only", "off"], "enumDescriptions": ["Enable all MCP functionality (server use and build instructions)", "Enable MCP server use only (excludes instructions about building MCP servers)", "Disable all MCP functionality"], "default": "full", "description": "Controls MCP inclusion in prompts, reduces token usage if you only need access to certain functionality."}, "cline.enableCheckpoints": {"type": "boolean", "default": true, "description": "Enables extension to save checkpoints of workspace throughout the task."}, "cline.disableBrowserTool": {"type": "boolean", "default": false, "description": "Disables extension from spawning browser session."}, "cline.modelSettings.o3Mini.reasoningEffort": {"type": "string", "enum": ["low", "medium", "high"], "default": "medium", "description": "Controls the reasoning effort when using the o3-mini model. Higher values may result in more thorough but slower responses."}, "cline.chromeExecutablePath": {"type": "string", "default": null, "description": "Path to Chrome executable for browser use functionality. If not set, the extension will attempt to find or download it automatically."}}}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "npm run check-types && npm run lint && node esbuild.js", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "npm run build:webview && npm run check-types && npm run lint && node esbuild.js --production", "compile-tests": "tsc -p ./tsconfig.test.json --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "npm run compile-tests && npm run compile && npm run lint", "check-types": "tsc --noEmit", "lint": "eslint src --ext ts", "format": "prettier . --check", "format:fix": "prettier . --write", "test": "vscode-test", "install:all": "npm install && cd webview-ui && npm install", "start:webview": "cd webview-ui && npm run start", "build:webview": "cd webview-ui && npm run build", "test:webview": "cd webview-ui && npm run test", "publish:marketplace": "vsce publish && ovsx publish", "publish:marketplace:prerelease": "vsce publish --pre-release && ovsx publish --pre-release", "prepare": "husky", "changeset": "changeset", "version-packages": "changeset version"}, "devDependencies": {"@changesets/cli": "^2.27.12", "@types/chai": "^5.0.1", "@types/diff": "^5.2.1", "@types/mocha": "^10.0.7", "@types/node": "20.x", "@types/should": "^11.2.0", "@types/vscode": "^1.84.0", "@typescript-eslint/eslint-plugin": "^7.14.1", "@typescript-eslint/parser": "^7.11.0", "@vscode/test-cli": "^0.0.9", "@vscode/test-electron": "^2.4.0", "chai": "^4.3.10", "esbuild": "^0.25.0", "eslint": "^8.57.0", "husky": "^9.1.7", "npm-run-all": "^4.1.5", "prettier": "^3.3.3", "should": "^13.2.3", "typescript": "^5.4.5"}, "dependencies": {"@anthropic-ai/bedrock-sdk": "^0.10.2", "@anthropic-ai/sdk": "^0.26.0", "@anthropic-ai/vertex-sdk": "^0.4.1", "@google/generative-ai": "^0.18.0", "@mistralai/mistralai": "^1.3.6", "@modelcontextprotocol/sdk": "^1.0.1", "@types/clone-deep": "^4.0.4", "@types/get-folder-size": "^3.0.4", "@types/pdf-parse": "^1.1.4", "@types/turndown": "^5.0.5", "@vscode/codicons": "^0.0.36", "axios": "^1.7.4", "cheerio": "^1.0.0", "chokidar": "^4.0.1", "clone-deep": "^4.0.1", "default-shell": "^2.2.0", "delay": "^6.0.0", "diff": "^5.2.0", "execa": "^9.5.2", "fast-deep-equal": "^3.1.3", "firebase": "^11.2.0", "get-folder-size": "^5.0.0", "globby": "^14.0.2", "ignore": "^7.0.3", "isbinaryfile": "^5.0.2", "mammoth": "^1.8.0", "monaco-vscode-textmate-theme-converter": "^0.1.7", "openai": "^4.83.0", "os-name": "^6.0.0", "p-wait-for": "^5.0.2", "pdf-parse": "^1.1.1", "puppeteer-chromium-resolver": "^23.0.0", "puppeteer-core": "^23.4.0", "serialize-error": "^11.0.3", "simple-git": "^3.27.0", "strip-ansi": "^7.1.0", "tree-sitter-wasms": "^0.1.11", "turndown": "^7.2.0", "web-tree-sitter": "^0.22.6", "zod": "^3.23.8"}}