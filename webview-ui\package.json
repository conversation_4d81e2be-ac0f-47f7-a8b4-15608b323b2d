{"name": "webview-ui", "version": "0.1.0", "private": true, "dependencies": {"@floating-ui/react": "^0.27.4", "@vscode/webview-ui-toolkit": "^1.4.0", "debounce": "^2.1.1", "fast-deep-equal": "^3.1.3", "fuse.js": "^7.0.0", "pretty-bytes": "^6.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-remark": "^2.1.0", "react-scripts": "^5.0.1", "react-textarea-autosize": "^8.5.3", "react-use": "^17.5.1", "react-virtuoso": "^4.7.13", "rehype-highlight": "^7.0.0", "rewire": "^7.0.0", "styled-components": "^6.1.13", "typescript": "^5.7.3", "web-vitals": "^2.1.4"}, "overrides": {"typescript": "^5.7.3"}, "scripts": {"start": "react-scripts start", "build": "node ./scripts/build-react-no-split.js", "test": "vitest run", "test:watch": "vitest dev", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^15.0.6", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^20.x", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/vscode-webview": "^1.57.5", "jsdom": "^25.0.1", "vitest": "^2.1.9"}}